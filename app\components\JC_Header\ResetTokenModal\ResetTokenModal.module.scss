@import '../../../global';

// Modal override class to remove padding and scrolling from main modal container
.modalOverride {
    padding-left: 0 !important;
    padding-right: 0 !important;
    overflow-y: hidden !important;
    display: flex !important;
    flex-direction: column !important;
}

.mainContainer {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 600px;
    flex: 1;
    padding: 0 30px;
    box-sizing: border-box;
    overflow-y: auto;
    @include hideScrollBar;
}

.pageTitle {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
}

.disclaimer {
    margin-top: 5px;
    display: flex;
    background-color: rgba($primaryColor, 0.1);
    border-radius: 8px;
    padding: 20px;

    /* Disclaimer icon removed */

    .disclaimerText {
        font-size: 14px;
        line-height: 1.4;

        p {
            margin-top: 0;
            margin-bottom: 10px;
            &:first-child {
                margin-top: 0;
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.instructions {
    margin-top: 0;
    background-color: rgba($offBlack, 0.05);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;

    p {
        margin-bottom: 10px;
        font-weight: bold;
    }

    ol {
        padding-left: 20px;

        li {
            margin-bottom: 8px;
            line-height: 1.4;
        }
    }
}

.resetButtonContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .resetButton {
        background-color: $primaryColor;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 10px 20px;
        font-size: 16px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
            background-color: darken($primaryColor, 10%);
        }

        &:disabled {
            background-color: lighten($primaryColor, 20%);
            cursor: not-allowed;
        }
    }

    .spinner {
        position: absolute;
        right: -40px;
        animation: spin 1s linear infinite;
    }
}

.curlButton {
    margin: 20px auto;
}

.inputGroup {
    margin-top: 10px;
    margin-bottom: 15px;

    .inputHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;

        label {
            font-weight: bold;
            font-size: 14px;
        }

        .pasteButton {
            background: none;
            border: none;
            color: $primaryColor;
            font-size: 12px;
            cursor: pointer;
            text-decoration: underline;

            &:hover {
                color: darken($primaryColor, 10%);
            }
        }
    }

    .tokenInput {
        width: 100%;
        padding: 8px;
        border: 1px solid $offBlack;
        border-radius: 4px;
        font-family: monospace;
        font-size: 14px;
        resize: vertical;
    }
}

.fullWidthInput {
    width: 100% !important;
    max-width: 100% !important;
}

.saveButtonContainer {
    display: flex;
    justify-content: center;
    margin: 12px auto 5px;
}

.statusMessage {
    padding: 10px;
    border-radius: 4px;
    text-align: center;
    margin-top: 20px;

    &.success {
        background-color: rgba(green, 0.1);
        color: darken(green, 10%);
    }

    &.error {
        background-color: rgba(red, 0.1);
        color: darken(red, 10%);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
